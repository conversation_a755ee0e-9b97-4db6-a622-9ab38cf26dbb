/**
 * GridItemBase.qml - Base component cho tất cả grid items
 *
 * Chức năng chính:
 * - Base properties và functions chung cho tất cả grid items
 * - Component loading system cho specific item types (camera, map, event)
 * - Common drag & drop functionality thông qua GridItemActionHandler
 * - Common resize functionality thông qua GridItemResizeHandlers
 * - <PERSON><PERSON> controls và context menu management
 * - Animation system integration
 * - State management (hover, selection, fullscreen)
 *
 * Architecture:
 * - OOP inheritance pattern - base class cho specialized components
 * - Component-based architecture với ActionHandler, ButtonControls, ResizeHandlers
 * - Signal-driven communication với parent components
 * - Property binding system cho reactive UI updates
 */

import QtQuick
import QtQuick.Controls
import QtQuick.Layouts
import models 1.0
import "../controls"
import "../handlers"
import "../components"
import "../base"
import "../constants/ZIndexConstants.js" as ZIndex

Rectangle {
    id: root
    visible: itemOpacity > 0
    // ✅ HYBRID: Item opacity + Canvas border combination
    color: (isDragging || isAnimatingMove || !isMaximized) ? "transparent" : backgroundColor
    property real itemOpacity: {
        if (isDragging) return 1
        if (isSelected) return 1  // Slight dimming for selected items
        return 1.0
    }

    // ✅ OPTIMIZED: AnimationManager reference passed from MainGrid
    property var animationManager: null
    // Apply custom opacity to avoid conflicts with Item.opacity
    opacity: itemOpacity
    scale: isDragging ? 1.0 : 1.0
    z: calculateZ()
    // border.color: "#000000"
    // border.width: 4
    
    property alias controlHandler: actionHandler

    property bool isDarkTheme: true
    property var gridModel: null
    property string itemType: "camera"
    property int gridRow: itemData ? itemData.row : 0
    property int gridCol: itemData ? itemData.col : 0
    property color backgroundColor: gridModel ? gridModel.get_color_theme_by_key("main_background") : "white"
    property bool isDragging: false
    property bool isHovered: false
    // ✅ SIMPLIFIED: Pure data binding - single source of truth
    property bool isSelected: itemData ? itemData.selected : false

    onIsSelectedChanged: {
        if (typeof timeLineManager !== 'undefined' && timeLineManager) {
            var controller = timeLineManager.timeLineController
        }
        updateParentZIndex()
    }

    onItemDataChanged: {
    }
    property bool isFocused: itemData ? itemData.focused : false  //Focus binding
    property bool isResizing: false
    property bool isVirtualGrid: false
    property bool isMaximized: root.itemData ? root.itemData.fullscreen : false
    property bool showLabels: true
    // property bool showControlButtons: true
    property string menuBackgroundColor: isDarkTheme ? "#2d2d2d" : "#ffffff"
    property string menuTextColor: isDarkTheme ? "#ffffff" : "#2b2a3a"
    property string menuHoverColor: isDarkTheme ? "#3f3f87" : "#f0f0f0"
    property string menuSeparatorColor: isDarkTheme ? "#656475" : "#cdcdcd"
    property string menuBorderColor: isDarkTheme ? "#656475" : "#cdcdcd"
    property real normalX: x
    property real normalY: y
    property real normalWidth: width
    property real normalHeight: height
    property real startWidth: 0
    property real startHeight: 0
    property real startX: 0
    property real startY: 0
    property var itemData: null
    property bool showControls: isHovered || isSelected
    property int position: itemData ? (itemData.row * (gridModel ? gridModel.columns : 12) + itemData.col) : -1
    property var contextMenu: null
    property string defaultBorderColor: "transparent"
    property string hoverBorderColor: isDarkTheme ? "#75FE8C" : "#75FE8C"
    property string selectedBorderColor: isDarkTheme ? "#75FE8C" : "#75FE8C"
    property bool dropSuccessful: false

    // ✅ NEW: Add animation support properties
    property real highlightOpacity: 0.0
    property bool isNewlyAdded: false
    property bool isAnimatingAdd: false
    property bool isAnimatingMove: false

    // ✅ CONTENT BOUNDS: Properties for actual content area (e.g., video frame within item bounds)
    property real contentBoundsX: 0
    property real contentBoundsY: 0
    property real contentBoundsWidth: root.width
    property real contentBoundsHeight: root.height

    // ✅ CONTENT BOUNDS SIGNALS: Emit when bounds change
    signal contentBoundsChanged(real x, real y, real width, real height)

    onContentBoundsXChanged: contentBoundsChanged(contentBoundsX, contentBoundsY, contentBoundsWidth, contentBoundsHeight)
    onContentBoundsYChanged: contentBoundsChanged(contentBoundsX, contentBoundsY, contentBoundsWidth, contentBoundsHeight)
    onContentBoundsWidthChanged: contentBoundsChanged(contentBoundsX, contentBoundsY, contentBoundsWidth, contentBoundsHeight)
    onContentBoundsHeightChanged: contentBoundsChanged(contentBoundsX, contentBoundsY, contentBoundsWidth, contentBoundsHeight)

    signal itemClicked(var item)
    signal itemDoubleClicked(var item)
    signal itemRightClicked(var item)
    signal dragStarted(var item)
    signal dragEnded(var item)
    signal resizeStarted(var item)
    signal resizeEnded(var item)
    signal hoverChanged(bool isHovered)
    signal swapRequested(int sourcePos, int targetPos)
    signal dragCompleted()
    signal visualFeedbackRequested(string type)

    /**
     * Calculate dynamic z-index based on item state
     */
    function calculateZ() {
        if (root.itemData && root.itemData.fullscreen) {
            return ZIndex.contentFullscreen
        }
        if (root.isDragging) {
            return ZIndex.contentDragged
        }
        if (root.isSelected) {
            return ZIndex.contentSelected
        }
        if (root.isHovered) {
            return ZIndex.contentHovered
        }
        return ZIndex.contentNormal
    }

    // Handle item selection with modifiers
    function handleItemSelection(item, ctrlPressed, shiftPressed) {
        if (!item || !gridModel) return

        var row = item.gridRow
        var col = item.gridCol

        if (shiftPressed) {
            // ✅ SIMPLIFIED: Shift+Click now just toggles selection (no range logic)
            gridModel.toggleSelectionAt(row, col)
        } else if (ctrlPressed) {
            gridModel.toggleSelectionAt(row, col)
        } else {
            gridModel.selectItemAt(row, col, false)
        }

        restoreFocusToGridActionHandler()
    }

    // Restore focus to GridActionHandler after selection
    function restoreFocusToGridActionHandler() {
        Qt.callLater(function() {
            var mainGrid = findMainGrid()
            if (mainGrid && mainGrid.gridActionHandler) {
                mainGrid.gridActionHandler.forceActiveFocus()
            }
        })
    }

    // Find MainGrid component in parent hierarchy
    function findMainGrid() {
        var current = root.parent
        while (current) {
            if (current.objectName === "MainGrid" || current.toString().indexOf("MainGrid") !== -1) {
                return current
            }
            current = current.parent
        }
        return null
    }

    onIsHoveredChanged: {
        updateParentZIndex()
    }

    onIsDraggingChanged: {
        updateParentZIndex()
    }

    GridItemActionHandler {
        id: actionHandler
        gridItem: root
        isDarkTheme: root.isDarkTheme
        isSelected: root.isSelected
        isMaximized: root.isMaximized || (root.itemData && root.itemData.fullscreen)
        isVirtualGrid: root.isVirtualGrid
        z: ZIndex.gridItemActionHandler
        
        anchors.fill: parent

        onHoverChanged: function(isHovered) {
            root.isHovered = isHovered
            root.hoverChanged(isHovered)
        }

        onItemClicked: function(item) {
            handleItemSelection(item, false, false)
        }

        onItemDoubleClicked: function(item) {
            // ✅ CENTRALIZED: Use reusable fullscreen handler
            if (root.itemData && root.animationManager) {
                var targetState = !root.itemData.fullscreen
                root.animationManager.handleFullscreenTransition(
                    root, targetState, "BASE_DOUBLE_CLICK"
                )
            }
        }

        onGridScaleRequested: function(delta) {
            if (root.parent && root.parent.gridLinesOverlay) {
                root.parent.gridLinesOverlay.startCtrlWheelMode()
            }
            if (root.gridModel) {
                root.gridModel.handleCtrlWheel(delta)
            }
        }
    }

    // ✅ OOP REFACTOR: Remove generic button controls from base
    // Each specialized component (GridItemCamera, GridItemMap2D, GridItemDigitalMap)
    // should implement their own specific button controls

    AnimationManager {
        id: animationManager
    }

    property var sizeAnimationManager: animationManager.createSizeAnimationManager(root)

    Connections {
        target: sizeAnimationManager
        function onAnimationStarted(animationType, toWidth, toHeight) {
        }

        function onAnimationCompleted(animationType, finalWidth, finalHeight) {
        }

        function onAnimationCancelled(animationType) {
        }
    }



    // ✅ OPTIMIZED: Resize handlers với Loader - chỉ khi click vào item
    Loader {
        id: resizeHandlersLoader
        anchors.fill: parent
        z: ZIndex.gridItemControls + 1
        active: root.isSelected && (root.itemData && !root.itemData.fullscreen)

        sourceComponent: Component {
            GridItemResizeHandlers {
                gridItem: root
                gridModel: root.gridModel
                itemData: root.itemData

                // ✅ CONTENT BOUNDS: Pass content bounds to resize handlers
                contentBoundsX: root.contentBoundsX
                contentBoundsY: root.contentBoundsY
                contentBoundsWidth: root.contentBoundsWidth
                contentBoundsHeight: root.contentBoundsHeight

                onResizeStarted: function(resizeType) {
                    isResizing = true
                }

                onResizeCompleted: function(resizeType) {
                    isResizing = false
                }
            }
        }
    }


    Connections {
        target: itemData

        // function onSizeChanged(newWidthCells, newHeightCells) {
        //     gridModel.isSave = false
        //     var newWidth = newWidthCells * root.parent.parent.width / gridModel.columns
        //     var newHeight = newHeightCells * root.parent.parent.height / gridModel.rows
        //     if (sizeAnimationManager.animateManualResize(newWidth, newHeight, 250)) {
        //         root.width = newWidth
        //         root.height = newHeight
        //     } else {
        //         root.width = newWidth
        //         root.height = newHeight
        //     }
        // }
    }
    Connections {
        target: gridModel
        function onThemeChanged() {
            backgroundColor = gridModel.get_color_theme_by_key("main_background")
        }
    }
    Connections {
        target: gridModel
        function onGridSizeAnimationRequested(oldCols, oldRows, newCols, newRows) {
            if (!itemData || !parent || !parent.parent) return
            var newWidth = itemData.cols_cell * parent.parent.width / newCols
            var newHeight = itemData.rows_cell * parent.parent.height / newRows
            animateForGridScale(newWidth, newHeight)
        }
    }

    function snapToGridRowCol(row, col) {
        if (!gridModel) return
        root.x = 0
        root.y = 0
    }

    Component.onCompleted: {
        scale = 1
    }

    Component.onDestruction: {
        if (root.itemData && root.itemData.gridItemSelected && root.itemData.gridItemSelected.widget === root) {
            root.itemData.gridItemSelected.widget = null
        }
    }

    function animateToFullscreen(targetWidth, targetHeight) {
        return sizeAnimationManager.animateFullscreen(targetWidth, targetHeight)
    }

    function animateFromFullscreen(targetWidth, targetHeight) {
        return sizeAnimationManager.animateFullscreen(targetWidth, targetHeight)
    }

    function animateForSwap(targetWidth, targetHeight) {
        return sizeAnimationManager.animateSwap(targetWidth, targetHeight)
    }

    function animateForGridScale(targetWidth, targetHeight) {
        return sizeAnimationManager.animateGridScale(targetWidth, targetHeight)
    }

    function animateForAutoLayout(targetWidth, targetHeight) {
        return sizeAnimationManager.animateAutoLayout(targetWidth, targetHeight)
    }

    function animateForWindowResize(targetWidth, targetHeight) {
        return sizeAnimationManager.animateWindowResize(targetWidth, targetHeight)
    }

    function animateForResponsive(targetWidth, targetHeight) {
        return sizeAnimationManager.animateResponsive(targetWidth, targetHeight)
    }

    function animateToSize(targetWidth, targetHeight, animationType, options) {
        return sizeAnimationManager.animateToSize(targetWidth, targetHeight, animationType, options)
    }

    // ✅ CLEAN: Simplified animation functions
    function startAddAnimation() {
        isAnimatingAdd = true
        isNewlyAdded = true
        opacity = 0
        scale = 0.9

        var fadeAnim = animationManager.createFadeInAnimation(root, 250)
        var scaleAnim = animationManager.createScaleAnimation(root, 0.9, 1.0, 250)

        fadeAnim.start()
        scaleAnim.start()

        fadeAnim.finished.connect(function() {
            startHighlightAnimation()
        })
    }

    function startHighlightAnimation() {
        var highlightAnim = animationManager.createFadeInAnimation(highlightBorder, 200)
        highlightAnim.finished.connect(function() {
            var fadeOutAnim = animationManager.createFadeOutAnimation(highlightBorder, 400)
            fadeOutAnim.finished.connect(function() {
                isAnimatingAdd = false
                isNewlyAdded = false
            })
            fadeOutAnim.start()
        })
        highlightAnim.start()
    }

    function setFullScreen(fullscreen) {
        if (itemData) {
            itemData.fullScreen = fullscreen
        }
    }


    Component {
        id: genericContextMenuComponent

        GridItemContextMenuBase {
            gridItem: root
            gridModel: root.gridModel
            position: root.position
            itemType: root.itemType
            z: ZIndex.gridItemContextMenu
        }
    }

    // ✅ CONTENT BOUNDS BORDER: Canvas positioned within actual content area
    Canvas {
        id: unifiedBorder
        // ✅ SYNCHRONIZED: Border follows content bounds exactly - stays within bounds
        x: root.contentBoundsX
        y: root.contentBoundsY
        width: root.contentBoundsWidth
        height: root.contentBoundsHeight
        z: ZIndex.gridItemOverlay + 1

        // ✅ HIDE DURING ANIMATION: Hide border during fullscreen/collapse transitions
        opacity: {
            // Hide border during fullscreen transitions
            if (root.itemData && root.itemData.isAnimating) return 0.0

            if (root.isFocused) return 1.0
            if (root.isSelected) return 1.0
            if (root.highlightOpacity > 0) return root.highlightOpacity
            if (typeof blinkOpacity !== 'undefined')
                return blinkOpacity
            return 0.0
        }

        Component.onCompleted: {
            requestPaint()
        }

        onWidthChanged: requestPaint()
        onHeightChanged: requestPaint()

        // Trigger repaint khi state thay đổi
        Connections {
            target: root
            function onIsSelectedChanged() { unifiedBorder.requestPaint() }
            function onIsFocusedChanged() { unifiedBorder.requestPaint() }
            function onHighlightOpacityChanged() { unifiedBorder.requestPaint() }
        }

        onPaint: {
            var ctx = getContext("2d")
            ctx.clearRect(0, 0, width, height)

            // Border inside with uniform thickness
            var borderThickness = 2
            var borderOffset = borderThickness / 2

            // Priority order: Focus > Selection > Highlight
            if (root.isFocused) {
                // ✅ FOCUS: Dashed orange border inside content bounds
                ctx.strokeStyle = "#ffaa00"
                ctx.lineWidth = borderThickness
                ctx.setLineDash([4, 2])
                ctx.beginPath()
                ctx.rect(borderOffset, borderOffset, width - borderThickness, height - borderThickness)
                ctx.stroke()
                ctx.setLineDash([]) // Reset dash
            } else if (root.isSelected) {
                // ✅ SELECTION: Primary green border inside content bounds
                ctx.strokeStyle = "#75FE8C"
                ctx.lineWidth = borderThickness
                ctx.strokeRect(borderOffset, borderOffset, width - borderThickness, height - borderThickness)
            } else if (root.highlightOpacity > 0) {
                // ✅ HIGHLIGHT: Green border inside content bounds
                ctx.strokeStyle = "#22C55E"
                ctx.lineWidth = borderThickness
                ctx.strokeRect(borderOffset, borderOffset, width - borderThickness, height - borderThickness)
            } else if (gridModel.tabType === CommonEnum.TRACKINGVIEW) {
                // ✅ TRACKING: Animated border inside content bounds
                ctx.strokeStyle = getAnimationColor()
                ctx.lineWidth = borderThickness
                ctx.strokeRect(borderOffset, borderOffset, width - borderThickness, height - borderThickness)
            }
        }
    }

    /**
     * Centralized method to update parent Loader's z-index
     */
    function updateParentZIndex() {
        if (!parent || parent.z === undefined) return
        
        if (isDragging) {
            parent.z = ZIndex.contentDragged
        } else if (itemData && itemData.fullscreen) {
            parent.z = ZIndex.contentFullscreen
        } else if (isSelected) {
            parent.z = ZIndex.contentSelected
            console.log
        } else if (isHovered) {
            parent.z = ZIndex.contentHovered
        } else {
            parent.z = ZIndex.contentNormal
        }
    }

    /**
     * Force update z-index to a specific value (for PTZ, etc.)
     */
    function forceParentZIndex(zValue) {
        if (parent && parent.z !== undefined) {
            parent.z = zValue
        }
    }

    property color primaryColor: gridModel ? gridModel.get_color_theme_by_key("primary") : "white"

    Loader {
        id: options
        z: ZIndex.gridItemOverlay + 2
        // anchors.fill: parent
        sourceComponent: getComponent()
    }
    Component {
        id: warningId
        Rectangle{
            visible: root.itemData.row === 0 && root.itemData.col === 0
            anchors.top: parent.top
            anchors.left: parent.left
            anchors.margins: 10
            width: content.width + 20
            height: content.height + 5
            color: Qt.rgba(255, 0, 0, 0.38)
            radius: 10
            z: ZIndex.gridItemOverlay + 2
            Text{
                id: content
                anchors.centerIn: parent
                text: qsTr("Intrusion Detected!")
                color: "white"
                font.pixelSize: 14
                font.bold: true
            }
        }
    }
    function getComponent(){
        if(root.itemData){
            if (gridModel.tabType === CommonEnum.TRACKINGVIEW){
                if (root.itemData.row === 0 && root.itemData.col === 0) {
                    return warningId
                }
            }
        }
        return null
    }

    function getAnimationColor(){
        if(root.itemData){
            if (gridModel.tabType === CommonEnum.TRACKINGVIEW){
                if (root.itemData.row === 0 && root.itemData.col === 0) {
                    return "red"
                }
                else if (root.itemData.row === 1 && root.itemData.col === 2){
                    return primaryColor
                }
            }
        }
        return "transparent"
    }
    
    Behavior on x {
        NumberAnimation {
            duration: 300
            onStarted: { root.isAnimatingMove = true }
            onStopped: { root.isAnimatingMove = false }
        }
    }
    Behavior on y {
        NumberAnimation {
            duration: 300
            onStarted: { root.isAnimatingMove = true }
            onStopped: { root.isAnimatingMove = false }
        }
    }
}
