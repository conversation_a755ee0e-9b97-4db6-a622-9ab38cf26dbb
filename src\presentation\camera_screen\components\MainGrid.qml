/**
 * MainGrid.qml - Main Camera Grid Container
 *
 * Ch<PERSON><PERSON> năng chính:
 * - Container ch<PERSON>h cho camera grid với responsive layout
 * - Drag & drop support cho camera, group, multi-selection
 * - Grid lines overlay với auto-hide functionality
 * - Fullscreen animation management
 * - Grid action handling (delete, fullscreen, context menus)
 * - Dynamic component loading dựa trên item type
 *
 * Architecture:
 * - Sử dụng Repeater với QAbstractListModel binding
 * - Row/col-based positioning system
 * - Component-based architecture với GridActionHandler, GridLinesOverlay
 * - Animation management thông qua AnimationManager
 */

import QtQuick 2.15
import QtQuick.Controls 2.15
import "../base"
import "../camera"
import "../constants/ZIndexConstants.js" as ZIndex
import "." as Components
import models 1.0

Item {
    id: mainGrid
    objectName: "MainGrid"
    focus: true
    property var gridModel: null
    property bool gridModelReady: gridModel !== null
    property bool isDarkTheme: gridModel ? gridModel.isDarkTheme : true
    property color backgroundColor: gridModel ? gridModel.get_color_theme_by_key("main_background") : "white"
    property color foregroundColor: gridModel ? gridModel.get_color_theme_by_key("text_color_all_app") : "white"
    property bool isVirtualGrid: false
    property bool showLabels: true
    // property bool showControlButtons: true

    //Expose gridActionHandler for forwarding
    property alias gridActionHandler: gridActionHandler

    Connections {
        target: gridModel
        function onThemeChanged() {
            backgroundColor = gridModel.get_color_theme_by_key("main_background")
        }
    }
    InputDialogSavedView {
        id: newSavedViewDialog
        z: 1000
        anchors.centerIn: parent
        onAccepted: (inputText) => {
            // Luôn gọi hàm saveSelectedItemsAsNewView, vì logic chọn item đã được xử lý ở context menu
            gridModel.saveSelectedItemsSavedView(inputText)
            
            // Reset và ẩn dialog
            newSavedViewDialog.inputText = ""
            newSavedViewDialog.visible = false
        }
        onRejected: () => {
            newSavedViewDialog.inputText = ""
        }
    }

    InputDialogVirtualWindow {
        id: newVirtualWindowDialog
        z: 1000
        anchors.centerIn: parent
        onAccepted: {
            gridModel.saveSelectedItemsVirtualWindow(inputText)
            inputText = ""
        }
        onRejected: {
            inputText = ""
        }
    }

    // ✅ OPTIMIZED: Animation Manager with centralized fullscreen logic
    AnimationManager {
        id: animationManager
        itemsContainer: itemsContainer
    }

    // ✅ NEW: Add Camera Animation Controller
    QtObject {
        id: addCameraAnimationController

        property bool isAnimating: false
        property var currentAnimation: null
        property var pendingOperations: []

        // Main entry point cho tất cả add operations
        function handleCameraAdd(cameras, addType, dropPosition) {
            if (isAnimating) {
                // Queue operation nếu đang animate
                pendingOperations.push({
                    cameras: cameras,
                    type: addType,
                    position: dropPosition
                })
                return
            }

            isAnimating = true

            switch(addType) {
                case "single_drop":
                    animateSingleCameraAdd(cameras[0], dropPosition)
                    break
                case "batch_add":
                    animateBatchCameraAdd(cameras)
                    break
                case "auto_fill":
                    animateAutoFillCameras(cameras)
                    break
                default:
                    // Fallback to single add
                    if (cameras && cameras.length > 0) {
                        animateSingleCameraAdd(cameras[0], dropPosition)
                    } else {
                        onAnimationComplete()
                    }
                    break
            }
        }

        function animateSingleCameraAdd(camera, dropPos) {
            // 1. Check if grid expansion needed (simplified for now)
            var needsExpansion = false // TODO: Implement grid expansion detection

            if (needsExpansion) {
                // Grid expansion + camera add
                var expansionAnim = mainGrid.animationManager.createGridExpansionAnimation(
                    gridModel.columns, gridModel.rows,
                    gridModel.columns + 1, gridModel.rows + 1, 450
                )

                expansionAnim.finished.connect(function() {
                    var cameraAnim = mainGrid.animationManager.createSingleCameraAddAnimation(camera, dropPos, 450)
                    cameraAnim.finished.connect(onAnimationComplete)
                    cameraAnim.start()
                })

                currentAnimation = expansionAnim
                expansionAnim.start()
            } else {
                // Direct camera add - simplified approach
                onAnimationComplete() // Skip animation for now
            }
        }

        function animateBatchCameraAdd(cameras) {
            // 1. Pre-calculate grid expansion (simplified for now)
            var needsExpansion = false // TODO: Implement batch expansion detection

            // 2. Simplified batch animation - just complete for now
            onAnimationComplete()
        }

        function animateAutoFillCameras(cameras) {
            // Use batch animation for auto fill
            animateBatchCameraAdd(cameras)
        }

        function onAnimationComplete() {
            isAnimating = false
            currentAnimation = null

            // Process pending operations
            if (pendingOperations.length > 0) {
                var nextOp = pendingOperations.shift()
                handleCameraAdd(nextOp.cameras, nextOp.type, nextOp.position)
            }
        }

        function cancelCurrentAnimation() {
            if (currentAnimation && isAnimating) {
                currentAnimation.stop()
                onAnimationComplete()
            }
        }
    }

    // === BLINK OPACITY TIMER (for TRACKINGVIEW) ===
    property real blinkOpacity: 1.0
    Timer {
        id: blinkTimer
        interval: 1000
        running: gridModel ? gridModel.tabType === CommonEnum.TRACKINGVIEW : false // chỉ trigger khi MainGrid này là màn tracking
        repeat: true
        onTriggered: {
            blinkOpacity = (blinkOpacity === 1.0) ? 0.3 : 1.0
        }
    }

    function setGridModel(model) {
        gridModel = model
    }

    function handleGridAction(actionType, actionData) {
        switch (actionType) {
            case "delete_selected":
                handleDeleteSelected()
                break
            case "toggle_fullscreen":
                handleToggleFullscreen()
                break
            case "hide_context_menus":
                handleHideContextMenus()
                break
            case "exit_fullscreen":
                handleExitFullscreen()
                break
        }
    }

    /**
     * Xử lý xóa các items đã chọn
     */
    function handleDeleteSelected() {
        if (gridModel) {
            // TODO: Implement delete selected items
        }
    }

    /**
     * Toggle fullscreen cho active item
     */
    function handleToggleFullscreen() {
        if (gridModel) {
            // TODO: Implement toggle fullscreen
        }
    }

    /**
     * Ẩn tất cả context menus đang mở
     */
    function handleHideContextMenus() {
        // TODO: Hide any open context menus in grid items
    }

    /**
     * Thoát fullscreen cho tất cả items
     */
    function handleExitFullscreen() {
        if (gridModel) {
            // TODO: Implement exit all fullscreen
        }
    }

    // ✅ REMOVED: handleSelectAll - Ctrl+A now handled directly in GridActionHandler

    /**
     * ✅ GRID LIMITS: Show notification for partial drop
     */
    function showPartialDropNotification(added, rejected) {
        console.log("⚠️ [GRID_LIMIT] Partial drop completed:")
        console.log("  - Added:", added, "cameras")
        console.log("  - Rejected:", rejected, "cameras (grid full)")
        console.log("  - Grid is now at maximum 12x12 capacity")

        // TODO: Show user-friendly notification toast/dialog
        // Example: "Added 15 cameras. 6 cameras rejected - grid is full (12x12 limit)"
    }

    /**
     * ✅ GRID LIMITS: Show notification when grid is full
     */
    function showGridFullNotification() {
        console.log("🚫 [GRID_LIMIT] Grid is full - cannot add more cameras")
        console.log("  - Current capacity: 12x12 = 144 cameras maximum")
        console.log("  - Please remove some cameras to add new ones")

        // TODO: Show user-friendly notification toast/dialog
        // Example: "Grid is full! Maximum 144 cameras allowed in 12x12 grid."
    }

    property int baseColumns: 1
    property int baseRows: 1
    property int maxColumns: 12
    property int maxRows: 12



    // Main container
    Rectangle {
        id: mainContainer
        anchors.fill: parent
        color: backgroundColor

        border.width: 0
        border.color: "transparent"
        Item {
            id: itemsContainer
            anchors.fill: parent
            // anchors.margins: 4
            Repeater {
                id: itemsRepeater
                model: (function() {
                    return (gridModel && gridModel.listGridItems) ? gridModel.listGridItems : null
                })()

                delegate: Loader {
                    id: itemLoader
                    property real cellSpacing: 0
                    property int columns: gridModel ? gridModel.columns : 1
                    property int rows: gridModel ? gridModel.rows : 1
                    property real cellWidth: (itemsContainer.width - (columns - 1) * cellSpacing) / columns
                    property real cellHeight: (itemsContainer.height - (rows - 1) * cellSpacing) / rows

                    property int gridX: (modelData && modelData.col !== undefined) ? modelData.col : 0
                    property int gridY: (modelData && modelData.row !== undefined) ? modelData.row : 0
                    property int colSpan: modelData ? modelData.cols_cell || 1 : 1
                    property int rowSpan: modelData ? modelData.rows_cell || 1 : 1

                    property real currentX: 0
                    property real currentY: 0

                    x: {
                        if (modelData?.isAnimating) return currentX
                        var defaultX = gridX * (cellWidth + cellSpacing) + cellSpacing / 2
                        var newX = animationManager.getPosition(modelData, defaultX)
                        currentX = newX
                        return newX
                    }

                    y: {
                        if (modelData?.isAnimating) return currentY
                        var defaultY = gridY * (cellHeight + cellSpacing) + cellSpacing / 2
                        var newY = animationManager.getPosition(modelData, defaultY)
                        currentY = newY
                        return newY
                    }
                    Behavior on x { NumberAnimation { duration: 300 } }
                    Behavior on y { NumberAnimation { duration: 300 } }


                    Behavior on width { enabled: !item.isResizing; NumberAnimation { duration: 300 } }
                    Behavior on height { enabled: !item.isResizing; NumberAnimation { duration: 300 } }

                    property real currentWidth: 100
                    property real currentHeight: 100

                    width: {
                        if (modelData?.isAnimating) return currentWidth
                        var defaultWidth = cellWidth * colSpan + cellSpacing * (colSpan - 1)
                        var newWidth = animationManager.getSize(modelData, defaultWidth, itemsContainer.width)
                        currentWidth = newWidth
                        return newWidth
                    }

                    height: {
                        if (modelData?.isAnimating) return currentHeight
                        var defaultHeight = cellHeight * rowSpan + cellSpacing * (rowSpan - 1)
                        var newHeight = animationManager.getSize(modelData, defaultHeight, itemsContainer.height)
                        currentHeight = newHeight
                        return newHeight
                    }

                    // Calculate dynamic z-index based on item state
                    z: {
                        if (!modelData) return ZIndex.contentNormal
                        
                        // Check for active PTZ state first
                        if (modelData.isPtzActive || modelData.isPtz3dActive || modelData.isDragZoomActive) {
                            return ZIndex.gridItemPopup // Use high z-index for active items
                        }
                        
                        // Check for fullscreen
                        if (modelData.fullscreen) {
                            return ZIndex.contentFullscreen
                        }
                        
                        // Check for selected state
                        if (modelData.selected) {
                            return ZIndex.contentSelected
                        }
                        
                        // Check for hover state - use isHovered instead of hovered
                        if (modelData.isHovered) {
                            return ZIndex.contentHovered
                        }
                        
                        return ZIndex.contentNormal
                    }

                    //Update modelData size when QML size changes
                    onWidthChanged: {
                        if (modelData && modelData.width !== width) {
                            modelData.width = width
                        }
                    }
                    onHeightChanged: {
                        if (modelData && modelData.height !== height) {
                            modelData.height = height
                        }
                    }
                    onXChanged: {
                        if (modelData && modelData.x !== x) {
                            modelData.x = x
                        }
                    }
                    onYChanged: {
                        if (modelData && modelData.y !== y) {
                            modelData.y = y
                        }
                    }

                    sourceComponent: {
                        if (!modelData) return null
                        var itemType = modelData.itemType
                        switch (itemType) {
                            case "camera":
                                return cameraComponent
                            case "list_map":
                                console.log("digitalMapComponent")
                                return digitalMapComponent
                            case "flooritem":
                                console.log("floorMapComponent")
                                return floorMapComponent
                            case "event":
                                console.log("eventComponent")
                                return eventComponent
                            default:
                                return baseComponent
                        }
                    }
                    onLoaded: {
                        if (item && modelData) {
                            // console.log("🎬 [MAIN_GRID] Starting add animation for new item at ",modelData)
                            item.itemData = modelData
                            item.isDarkTheme = mainGrid.isDarkTheme
                            item.gridModel = mainGrid.gridModel
                            item.isVirtualGrid = mainGrid.isVirtualGrid
                            item.showLabels = mainGrid.showLabels
                            // ✅ OPTIMIZED: Pass animationManager reference to item
                            item.animationManager = animationManager
                            // item.showControlButtons = mainGrid.showControlButtons
                            // ✅ NEW: Check if this is a newly added item and start animation
                            if (modelData.isNewlyAdded) {
                                // Delay animation slightly to ensure item is fully loaded
                                Qt.callLater(function() {
                                    // Tạm thời chưa dùng animation khi kéo item vào lưới để ưu tiên về mặt hiệu năng trước.
                                    // if (item && typeof item.startAddAnimation === "function") {
                                    //     item.startAddAnimation()
                                    // }
                                    // Reset the flag
                                    modelData.isNewlyAdded = false
                                })
                            }

                            if (item.itemData && item.itemData.hasOwnProperty("fullscreenChanged")) {
                                item.itemData.fullscreenChanged.connect(function() {
                                    handleItemFullscreen(item, itemLoader)
                                })
                            }
                        }
                    }

                    // Truyền blinkOpacity xuống các item con
                    property real blinkOpacity: mainGrid.blinkOpacity
                }
            }
        }
        
        DropArea {
            id: dropArea
            anchors.fill: parent
            keys: ["application/camera", "application/group", "application/multi-selection", "application/list_map","application/flooritem","application/event"]
            z: ZIndex.contentBackground

            onDropped: function(drop) {
                // console.log("Dropped:", JSON.stringify(drop, null, 2))
                gridModel.isSave = false
                var columns = gridModel ? gridModel.columns : 1
                var rows = gridModel ? gridModel.rows : 1
                var cellWidth = itemsContainer.width / columns
                var cellHeight = itemsContainer.height / rows
                var col = Math.floor(drop.x / cellWidth)
                var row = Math.floor(drop.y / cellHeight)

                if (drop.formats.includes("application/camera")) {
                    handleCameraDrop(drop, row, col)
                }
                else if (drop.formats.includes("application/group")) {
                    handleGroupDrop(drop, row, col)
                }
                else if (drop.formats.includes("application/multi-selection")) {
                    handleMultiSelectionDrop(drop, row, col)
                }
                else if (drop.formats.includes("application/list_map")) {
                    handleDigitalMapDrop(drop, row, col)
                }
                else if (drop.formats.includes("application/flooritem")) {
                    handleFloorMapDrop(drop, row, col)
                }
                else if (drop.formats.includes("application/event")) {
                    handleEventDrop(drop, row, col)
                }
            }
        }

        GridLinesOverlay {
            id: gridLinesOverlay
            anchors.fill: parent
            gridModel: mainGrid.gridModel
            isDarkTheme: mainGrid.isDarkTheme
        }

        //Selection overlay for drag selection
        GridSelectionOverlay {
            id: gridSelectionOverlay
            anchors.fill: parent
            gridModel: mainGrid.gridModel
            z: ZIndex.selectionOverlay

            onSelectionRectangleChanged: function(minX, minY, maxX, maxY) {
                // Forward to GridActionHandler for real-time selection
                if (gridActionHandler) {
                    gridActionHandler.updateDragSelection(minX, minY, maxX, maxY)
                }
            }

            onSelectionFinalized: function(startPos, endPos) {
                // Forward to GridActionHandler for final selection
                if (gridActionHandler) {
                    gridActionHandler.finalizeDragSelection(startPos.x, startPos.y, endPos.x, endPos.y)
                }
            }
        }

        GridActionHandler {
            id: gridActionHandler
            anchors.fill: parent
            gridModel: mainGrid.gridModel
            gridLinesOverlay: gridLinesOverlay
            dragHighlight: dragHighlight
            selectionOverlay: gridSelectionOverlay  //Connect selection overlay
            isDarkTheme: mainGrid.isDarkTheme
            z: ZIndex.contentBackground

            onActionRequested: function(actionType, actionData) {
                handleGridAction(actionType, actionData)
            }

            onDragStartRequested: function(item, mouseX, mouseY) {
            }

            onDragUpdateRequested: function(item, mouseX, mouseY) {
            }

            onDragEndRequested: function(item, mouseX, mouseY) {
            }
        }
    }

    /**
     * ✅ OPTIMIZED: Simplified fullscreen handler using centralized controller
     *
     * @param item - Grid item component
     * @param itemLoader - Loader component chứa item
     */
    function handleItemFullscreen(item, itemLoader) {
        if (!item || !item.itemData) return
        // Update grid lines visibility
        gridLinesOverlay.setFullscreenState(item.itemData.fullscreen)
    }








    /**
     * Xử lý drop camera từ camera list với animation
     *
     * @param drop - Drop event object
     * @param row - Target row trong grid
     * @param col - Target column trong grid
     */
    function handleCameraDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/x-qabstractitemmodeldatalist")
            if (rawData) {
                var cameraData = JSON.parse(rawData)
                if (gridModel && (cameraData.id || (cameraData.data && cameraData.data.id))) {
                    // Calculate drop position for animation
                    var cellWidth = itemsContainer.width / (gridModel.columns || 1)
                    var cellHeight = itemsContainer.height / (gridModel.rows || 1)
                    var dropPosition = {
                        x: col * cellWidth,
                        y: row * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    }

                    // Start animation before processing drop
                    addCameraAnimationController.handleCameraAdd([cameraData], "single_drop", dropPosition)

                    // Process the actual drop
                    gridModel.processHandleDrop(cameraData, "Camera")
                }
            }
        } catch (error) {
            console.error("Error handling camera drop:", error)
        }
    }

    function handleGroupDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/x-qabstractitemmodeldatalist")
            if (rawData) {
                var groupData = JSON.parse(rawData)
                if (gridModel && (groupData.id || (groupData.data && groupData.data.id))) {
                    gridModel.processHandleDrop(groupData, "Group")
                }
            }
        } catch (error) {
            console.error("Error handling group drop:", error)
        }
    }

    function handleMultiSelectionDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/json")
            if (rawData) {
                var multiData = JSON.parse(rawData)
                if (gridModel && multiData) {
                    // Determine if this is a batch operation
                    var cameras = multiData.cameras || [multiData]
                    var addType = cameras.length > 1 ? "batch_add" : "single_drop"

                    // Calculate drop position for animation
                    var cellWidth = itemsContainer.width / (gridModel.columns || 1)
                    var cellHeight = itemsContainer.height / (gridModel.rows || 1)
                    var dropPosition = {
                        x: col * cellWidth,
                        y: row * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    }

                    // Start animation before processing drop
                    addCameraAnimationController.handleCameraAdd(cameras, addType, dropPosition)

                    // Process the actual drop
                    gridModel.processHandleDrop(multiData, "multi-selection")
                }
            }
        } catch (error) {
            console.error("Error handling multi-selection drop:", error)
        }
    }

    function handleDigitalMapDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/x-qabstractitemmodeldatalist")
            if (rawData) {
                var mapData = JSON.parse(rawData)
                console.log("Processing map drop:", mapData)
                if (gridModel && (mapData.data && mapData.data.id)) {
                    console.log("Processing map drop:", mapData.data.id,mapData.type)
                    // Calculate drop position for animation
                    var cellWidth = itemsContainer.width / (gridModel.columns || 1)
                    var cellHeight = itemsContainer.height / (gridModel.rows || 1)
                    var dropPosition = {
                        x: col * cellWidth,
                        y: row * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    }

                    // Start animation before processing drop
                    addCameraAnimationController.handleCameraAdd([mapData], "single_drop", dropPosition)

                    // Process the actual drop
                    gridModel.processHandleDrop(mapData, "DigitalMap")
                }
            }
        } catch (error) {
            console.error("Error handling camera drop:", error)
        }
    }
    function handleFloorMapDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/x-qabstractitemmodeldatalist")
            if (rawData) {
                var mapData = JSON.parse(rawData)
                console.log("Processing map drop:", mapData)
                if (gridModel && (mapData.data && mapData.data.id)) {
                    console.log("Processing map drop:", mapData.data.id,mapData.type)
                    // Calculate drop position for animation
                    var cellWidth = itemsContainer.width / (gridModel.columns || 1)
                    var cellHeight = itemsContainer.height / (gridModel.rows || 1)
                    var dropPosition = {
                        x: col * cellWidth,
                        y: row * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    }

                    // Start animation before processing drop
                    addCameraAnimationController.handleCameraAdd([mapData], "single_drop", dropPosition)

                    // Process the actual drop
                    gridModel.processHandleDrop(mapData, "FloorMap")
                }
            }
        } catch (error) {
            console.error("Error handling camera drop:", error)
        }
    }
    function handleEventDrop(drop, row, col) {
        try {
            var rawData = drop.getDataAsString("application/event")
            if (rawData) {
                var eventData = JSON.parse(rawData)
                console.log("Processing map drop:", eventData)
                if (eventData) {
                    console.log("Processing event drop:", eventData.id,eventData.type)
                    // Calculate drop position for animation
                    var cellWidth = itemsContainer.width / (gridModel.columns || 1)
                    var cellHeight = itemsContainer.height / (gridModel.rows || 1)
                    var dropPosition = {
                        x: col * cellWidth,
                        y: row * cellHeight,
                        width: cellWidth,
                        height: cellHeight
                    }

                    // Start animation before processing drop
                    addCameraAnimationController.handleCameraAdd([eventData], "single_drop", dropPosition)

                    // Process the actual drop
                    gridModel.processHandleDrop(eventData, "Event")
                }
            }
        } catch (error) {
            console.error("Error handling camera drop:", error)
        }
    }
    Rectangle {
        id: dragHighlight
        objectName: "dragHighlight"
        visible: false
        color: "#5000ff5e"
        border.color: "#00ff5e"
        border.width: 2
        radius: 4
        z: 4

        property bool isDragActive: false
        property int targetCol: -1
        property int targetRow: -1
        property int targetWidth: 1
        property int targetHeight: 1

        function updateHighlight(col, row, width, height, isValid, isSwap) {
            if (!gridModel) return
            var cellWidth = parent.width / gridModel.columns
            var cellHeight = parent.height / gridModel.rows
            x = col * cellWidth
            y = row * cellHeight
            dragHighlight.width = width * cellWidth
            dragHighlight.height = height * cellHeight
            if (!isValid) {
                color = "#86e90909"
                border.color = "#FF4444"
                z = 4
            } else {
                color = "#6c01ff62"
                border.color = "#0e9241"
                z = 4
            }
            targetCol = col
            targetRow = row
            targetWidth = width
            targetHeight = height

            // Show highlight
            visible = true
            isDragActive = true
        }

        // Hide highlight
        function hideHighlight() {
            visible = false
            isDragActive = false
        }
    }

    // ✅ OPTIMIZED: Signal connections with performance improvements
    Connections {
        target: gridModel

        // function onActionCompleted(actionType, success, message) {
        //     console.log("Main grid action completed:", actionType, "Success:", success, "Message:", message)
        // }

        // ✅ Grid lines repaint is now handled by GridLinesOverlay component
        // No need for manual scheduleRepaint calls

        // ✅ DEPRECATED: Remove onGridChanged - use specific signals
        // function onGridChanged(columns, rows) {
        //     console.log("Main grid size changed:", columns, "x", rows)
        //     gridLines.requestPaint() // Redraw grid lines
        // }

        // ✅ GRID LIMITS: Handle partial drop notifications
        function onPartialDropCompleted(added, rejected) {
            showPartialDropNotification(added, rejected)
        }

        function onGridCapacityReached() {
            showGridFullNotification()
        }
    }
    
    // ✅ NEW: Drag management functions - Delegated to GridActionHandler
    // Function called when item starts dragging
    function startItemDrag(item, mouseXInItem, mouseYInItem) {
        return gridActionHandler.startDrag(item, mouseXInItem, mouseYInItem)
    }

    // Function called when mouse moves during drag
    function updateItemDrag(item, mouseXInItem, mouseYInItem) {
        // Calculate mouse position on MainGrid from item coordinates
        var mouseOnGrid = item.mapToItem(mainGrid, mouseXInItem, mouseYInItem)
        gridActionHandler.updateDrag(item, mouseOnGrid.x, mouseOnGrid.y)
    }

    // Function called when drag ends
    function endItemDrag(item, mouseXInItem, mouseYInItem) {
        // Calculate mouse position on MainGrid from item coordinates
        var mouseOnGrid = item.mapToItem(mainGrid, mouseXInItem, mouseYInItem)
        return gridActionHandler.endDrag(item, mouseOnGrid.x, mouseOnGrid.y)
    }

    // ✅ Wheel handling is now managed by GridActionHandler

    // ✅ Keyboard shortcuts are now handled by GridActionHandler
    
    // Component definitions for different item types
    Component {
        id: cameraComponent
        GridItemCamera {
            // Camera-specific component with inheritance from GridItemBase
        }
    }

    Component {
        id: digitalMapComponent
        GridItemDigitalMap {
        }
    }
    Component {
        id: floorMapComponent
        GridItemMap2D {
        }
    }
    Component {
        id: eventComponent
        GridItemEvent {
        }
    }

    Component {
        id: baseComponent
        GridItemBase {
            // Fallback to base component
        }
    }

    // ✅ NEW: Background MouseArea để tắt PTZ khi click outside

    Component.onCompleted: {
        // ✅ OPTIMIZED: Simplified focus management
        if (gridActionHandler) {
            gridActionHandler.forceActiveFocus()
        }

        // Focus restore on grid changes
        if (gridModel) {
            gridModel.listGridItemsChanged.connect(function() {
                if (gridActionHandler && !gridActionHandler.activeFocus) {
                    gridActionHandler.forceActiveFocus()
                }
            })
        }
    }

    

}
